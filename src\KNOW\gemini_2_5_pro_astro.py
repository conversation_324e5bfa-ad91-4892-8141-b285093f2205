import os
import json
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
from google import genai
from google.genai import types
import time
from tqdm import tqdm

# 載入 .env 中的 GEMINI_API_KEY
load_dotenv()
gemini_api_key = os.getenv("GEMINI_API_KEY")
if not gemini_api_key:
    with open("gemini-api.key.json", "r") as f:
        key_data = json.load(f)
        gemini_api_key = key_data.get("api_key")

if not gemini_api_key:
    raise ValueError("找不到 GEMINI_API_KEY，請確認 .env 檔案或 gemini-api.key.json 中已設定正確的 API 金鑰")

# System prompt in English to reformat existing content
system_prompt = """You are a highly skilled and experienced master astrologer with deep knowledge of astrology and extensive consultation experience.

I will provide you with the following components:
- question: The user's astrology-related, daily life or meaningful life question
- resource: The resource of related to the question

Your task is to reorganize this content using your professional astrological knowledge, deeply understanding the user's true inner needs and underlying concerns, and provide a complete, psychologically supportive astrological consultation response.

### thinking:
Analyze the original thinking process and enhance it by:
- Perceive the real motivations and inner needs behind the user's question
- Identify deeper concerns the user may not have directly expressed
- Understand the user's current life stage and psychological state
- Adding multi-angle thinking or self-debate
- Organizing thoughts into 2-3 clear approaches before summarizing
- If the original content is strong, preserve its key elements
- thinking step by step
- Apply expert knowledge of planets, signs, and houses
- Integrate aspect relationships and astrological cycles in analysis

### answer:
Provide insights and guidance based on astrological theory

Important Notes:
- Maintain the original language of the content (Chinese or English)
- Use Traditional Chinese for Chinese content
- Do NOT create new content - only reorganize existing content
- Present with an astrologer's professional perspective and warm tone

Your output must be formatted as a valid JSON object:
{  "question": "...", 
  "resource": "...",
  "thinking": "...",
  "answer": "..."
}"""

# 線程安全的檔案寫入鎖
write_lock = threading.Lock()

def detect_language(text):
    """判斷文本是中文還是英文"""
    # 簡單判斷：如果包含中文字符，則視為中文
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            return "zh"
    return "en"

def process_single_question(question_data, output_file):
    """處理單一問題的函數"""
    try:
        # 從問題數據中提取必要的信息
        question = ""
        resource = ""
        
        if isinstance(question_data, dict):
            # 提取問題
            if "question" in question_data:
                question = question_data["question"]
            elif "Q" in question_data:
                question = question_data["Q"]
            else:
                # 無法識別問題格式
                return {"status": "failed", "error": "無法識別問題格式"}
                
            # 提取資源
            if "resource" in question_data:
                resource = question_data["resource"]
            elif "r" in question_data:
                resource = question_data["r"]
                
            # 確保所有字段都有值
            if not question:
                return {"status": "failed", "error": "缺少問題"}
        else:
            return {"status": "failed", "error": "問題數據格式錯誤"}
        
        # 建立新的 client 實例以避免線程衝突
        client = genai.Client(api_key=gemini_api_key)
        
        print(f"🧠 處理問題：{question[:30]}...")

        # 準備 prompt，將所有現有內容傳給 Gemini
        prompt = f"""
{system_prompt}

Here is the content to reformat:

QUESTION:
{question}

RESOURCE:
{resource}

Please reformat this content according to the system prompt rules. Make sure to:
1. Enhance the thinking process with multi-angle analysis
2. Create clear step-by-step thinking
3. Restructure the answer to be well-organized
4. Output ONLY a valid JSON object with all four fields: question, resource, thinking, answer

IMPORTANT: Return ONLY the JSON object, no additional text or formatting.
"""

        # 呼叫 Gemini 模型
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt
        )

        # 獲取模型回應
        response_text = response.candidates[0].content.parts[0].text.strip()
        print(f"🔍 API 回應：{response_text[:200]}...")
        
        # 嘗試解析 JSON 回應
        try:
            # 清理回應文本，移除可能的 markdown 格式
            cleaned_response = response_text.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()
            
            # 檢查回應是否是 JSON 格式
            if cleaned_response.startswith("{") and cleaned_response.endswith("}"):
                parsed_response = json.loads(cleaned_response)
                
                # 確保所需的字段都存在
                result_data = {
                    "question": parsed_response.get("question", question),
                    "resource": parsed_response.get("resource", resource),
                    "thinking": parsed_response.get("thinking", ""),
                    "answer": parsed_response.get("answer", "")
                }
            else:
                # 如果不是 JSON 格式，嘗試從文本中提取部分
                print(f"⚠️ JSON 解析失敗，嘗試從文本中提取：{response_text[:100]}...")
                parsed_response = extract_sections_from_text(response_text)
                result_data = {
                    "question": question,
                    "resource": resource,
                    "thinking": parsed_response.get("thinking", ""),
                    "answer": parsed_response.get("answer", "")
                }
                
        except json.JSONDecodeError:
            # 如果不是有效的 JSON，嘗試從文本中提取部分
            print(f"⚠️ JSON 解析失敗，嘗試從文本中提取：{response_text[:100]}...")
            parsed_response = extract_sections_from_text(response_text)
            result_data = {
                "question": question,
                "resource": resource,
                "thinking": parsed_response.get("thinking", ""),
                "answer": parsed_response.get("answer", "")
            }

        # 使用線程鎖確保安全寫入
        with write_lock:
            with open(output_file, "a", encoding="utf-8") as out_file:
                # 直接寫入 JSON 格式，不包含額外的格式標記
                out_file.write(json.dumps(result_data, ensure_ascii=False) + "\n")
        
        return {"status": "success"}

    except Exception as e:
        print(f"❌ 錯誤：{e}")
        return {"status": "failed", "question": question if 'question' in locals() else "未知問題", "error": str(e)}

def extract_sections_from_text(text):
    """從文本中提取 thinking, answer 部分"""
    result = {}
    
    # 檢查英文標記
    for section in ["thinking", "answer"]:
        section_marker = f"{section}:"
        if section_marker.lower() in text.lower():
            start_idx = text.lower().find(section_marker.lower()) + len(section_marker)
            end_idx = None
            
            # 查找下一個部分的標記
            for next_section in ["thinking:", "answer:"]:
                if next_section.lower() in text.lower()[start_idx:]:
                    end_idx = text.lower()[start_idx:].find(next_section.lower()) + start_idx
                    break
            
            if end_idx:
                result[section] = text[start_idx:end_idx].strip()
            else:
                result[section] = text[start_idx:].strip()
        else:
            # 如果找不到標記，設為空字串
            result[section] = ""
    
    return result

def process_jsonl_file(input_file, output_file, max_workers=5, start_index=0, end_index=None):
    """處理單個JSONL文件"""
    try:
        # 讀取JSONL文件
        questions = []
        with open(input_file, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    questions.append(data)
                except json.JSONDecodeError:
                    print(f"警告：跳過無效的JSON行：{line[:50]}...")
        
        # 確定處理範圍
        if end_index is None:
            end_index = len(questions)
        
        questions_to_process = questions[start_index:end_index]
        total_questions = len(questions_to_process)
        
        print(f"📊 從 {input_file} 讀取了 {len(questions)} 個問題")
        print(f"📊 將處理 {total_questions} 個問題 (索引 {start_index} 到 {end_index-1})")
        print(f"🔧 使用 {max_workers} 個線程並行處理")
        
        # 創建輸出文件（如果不存在）
        with open(output_file, "w", encoding="utf-8") as _:
            pass  # 創建空文件
        
        failed_questions = []
        successful_count = 0
        
        # 使用 ThreadPoolExecutor 進行多線程處理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任務
            future_to_question = {
                executor.submit(process_single_question, question, output_file): question 
                for question in questions_to_process
            }
            
            # 使用 tqdm 顯示進度條
            with tqdm(total=total_questions, desc=f"處理 {input_file}") as pbar:
                for future in as_completed(future_to_question):
                    result = future.result()
                    
                    if result["status"] == "success":
                        successful_count += 1
                    else:
                        failed_questions.append(result)
                    
                    pbar.update(1)
                    pbar.set_postfix({
                        "成功": successful_count,
                        "失敗": len(failed_questions)
                    })

        print(f"\n✅ 完成處理 {input_file}！總問題數: {total_questions}，成功: {successful_count}，失敗: {len(failed_questions)}")

        # 儲存失敗的問題
        if failed_questions:
            timestamp = int(time.time())
            failed_file = f"failed_{os.path.basename(input_file)}_{timestamp}.json"
            with open(failed_file, "w", encoding="utf-8") as file:
                json.dump(failed_questions, file, ensure_ascii=False, indent=2)
            print(f"⚠️ 已輸出失敗問題至 {failed_file}")

        return True

    except Exception as e:
        print(f"❌ 處理文件 {input_file} 時發生錯誤：{e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 設定
    MAX_WORKERS = 5  # 測試時使用較少的線程數
    
    # 要處理的文件列表及其對應的輸出文件
    files_to_process = [
        {"input": "src/KNOW/question_list/testing_question.jsonl", "output": "src/KNOW/thinking strengthening/testing_strengthening_question.jsonl"}
    ]
    
    print("🚀 開始執行 Gemini API 處理程式")
    
    for file_info in files_to_process:
        input_file = file_info["input"]
        output_file = file_info["output"]
        
        print(f"\n🔍 開始處理文件：{input_file} -> {output_file}")
        
        # 確保輸出目錄存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        success = process_jsonl_file(
            input_file=input_file,
            output_file=output_file,
            max_workers=MAX_WORKERS,
            start_index=0,
            end_index=None
        )
        
        if success:
            print(f"🎉 文件 {input_file} 處理完成！")
        else:
            print(f"❌ 文件 {input_file} 處理失敗！")
    
    print("\n🎯 所有文件處理完畢！")