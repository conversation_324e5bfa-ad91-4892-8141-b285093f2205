# 思考能力增強訓練流程規劃

## 專案概述

**目標**：建立一個靈活的多層次思考增強系統，透過可調整的提示詞模板，讓不同 AI 模型逐步深化占星學問題的思考品質。

**核心理念**：從問題集 JSONL 檔案中，讓多個 AI 模型依序處理 "question"、"thinking"、"resource" 的脈絡，透過迭代增強來提升思考深度與品質。

## 核心流程架構

### 階段一：資料預處理

**輸入格式**：`testing_question.jsonl`

```json
{
  "question": "占星學問題",
  "resource": "相關參考資料",
  "resource_id": "書籍ID",
  "book_title": "書名",
  "chapter_title": "章節",
  "source": "來源",
  "template_used": "使用的模板"
}
```

**處理任務**：

- 資料格式驗證與清理(將"question":""去掉)
- 語言檢測（中文/英文）
- 資源品質初步評估

### 階段二：基礎思考生成（Gemini 2.5 Pro）

**功能**：建立初始思考框架

- 使用現有的 `gemini_2_5_pro_astro.py` 系統
- 生成結構化的基礎思考過程
- 建立多角度分析基礎

**可調整要素**：

- 基礎思考深度（輕度/中度/深度）
- 分析角度選擇
- 初始結構框架

### 階段三：思考深化（GPT-4o）

**功能**：專業強化與心理分析深化

- 心理動機分析強化
- 占星學技術面深化
- 增加自我辯證過程
- 多重思考路徑建立

**可調整要素**：

- 深化方向重點
- 心理分析程度
- 技術專業度
- 實用建議比重

### 階段四：思考整合（GPT-4.1）

**功能**：最終整合與實用化

- 不同思考面向整合
- 語言風格統一優化
- 實用性建議強化
- 專業度與溫暖度平衡

**可調整要素**：

- 整合策略重點
- 表達風格調整
- 文化敏感度
- 行動指引程度

## 技術實現規劃

### 1. 專案結構設計

```
src/KNOW/
├── core/
│   ├── data_processor.py      # 資料預處理模組
│   ├── gemini_enhancer.py     # Gemini思考生成器
│   ├── gpt4o_deepener.py      # GPT-4o思考深化器
│   ├── gpt41_integrator.py    # GPT-4.1最終整合器
│   └── pipeline_controller.py # 流程控制器
├── prompts/
│   ├── templates/
│   │   ├── gemini_prompts.py      # Gemini提示詞模板
│   │   ├── gpt4o_prompts.py       # GPT-4o提示詞模板
│   │   └── gpt41_prompts.py       # GPT-4.1提示詞模板
│   └── prompt_manager.py          # 提示詞動態管理器
├── config/
│   └── settings.py                # 全局設定
├── utils/
│   ├── file_handler.py            # 檔案處理工具
│   ├── language_detector.py       # 語言檢測工具
│   └── error_handler.py           # 錯誤處理工具
├── tests/
│   └── test_pipeline.py           # 測試模組
```

### 2. 簡化配置系統

系統將使用內建的預設配置，透過程式碼中的常數和函數參數來控制處理行為，避免複雜的 YAML 配置檔案。

**配置方式**：

- 透過函數參數直接調整處理行為
- 使用預定義的配置常數
- 支援運行時動態調整

### 3. 動態提示詞管理系統

#### 提示詞管理器 (`prompt_manager.py`)

```python
from typing import Dict, Any, Optional

class PromptManager:
    """簡化提示詞管理器"""

    def __init__(self):
        self.prompt_templates = self.load_prompt_templates()

    def load_prompt_templates(self) -> Dict[str, Any]:
        """載入內建提示詞模板"""
        # 實作模板載入邏輯
        pass

    def get_gemini_prompt(self,
                         question_type: str,
                         depth_level: str = "moderate") -> str:
        """根據問題類型和深度等級生成Gemini提示詞"""
        # 實作Gemini提示詞生成邏輯
        pass

    def get_gpt4o_prompt(self,
                        previous_thinking: str,
                        question_type: str,
                        focus_areas: Optional[list] = None) -> str:
        """根據前階段思考和重點領域生成GPT-4o提示詞"""
        # 實作GPT-4o提示詞生成邏輯
        pass

    def get_gpt41_prompt(self,
                        all_previous_thinking: Dict[str, str],
                        question_type: str,
                        style: str = "balanced") -> str:
        """根據所有前階段思考和風格生成GPT-4.1提示詞"""
        # 實作GPT-4.1提示詞生成邏輯
        pass

    def get_template_version(self, model: str, template_type: str) -> str:
        """取得指定模型和類型的範本版本"""
        # 實作版本管理邏輯
        pass
```

### 4. 多線程處理架構

#### 流程控制器 (`pipeline_controller.py`)

```python
from concurrent.futures import ThreadPoolExecutor
import threading
from typing import List, Dict, Any

class PipelineController:
    """主要流程控制器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_workers = config.get('max_workers', 4)
        self.lock = threading.Lock()

    def process_batch(self, input_data: List[Dict]) -> List[Dict]:
        """批次處理資料"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [
                executor.submit(self.process_single_item, item)
                for item in input_data
            ]
            results = [future.result() for future in futures]
        return results

    def process_single_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """處理單一項目的完整流程"""
        try:
            # 階段一：預處理
            processed_item = self.preprocess_data(item)

            # 階段二：Gemini基礎思考
            gemini_result = self.gemini_thinking(processed_item)

            # 階段三：GPT-4o深化
            gpt4o_result = self.gpt4o_enhancement(gemini_result)

            # 階段四：GPT-4.1整合
            final_result = self.gpt41_integration(gpt4o_result)

            return final_result

        except Exception as e:
            return self.handle_error(item, e)
```

## 輸出格式設計

### 最終輸出格式 (`result1.jsonl`)

### 最終輸出格式 (`result2.jsonl`)

### 最終輸出格式 (`result3.jsonl`)

```json
{
  "question": "原始占星學問題",
  "thinking": "基礎思考框架內容",
  "resource": "原始參考資料"
}
```

## 使用方式設計

### 1. 命令行介面使用

```bash
# 基本使用（使用預設設定）
python main.py --input testing_question.jsonl --output results.jsonl

# 自訂Gemini思考深度
python main.py --input data.jsonl --output results.jsonl --gemini-depth deep

# 自訂GPT-4o強化重點
python main.py --input data.jsonl --output results.jsonl --gpt4o-focus psychological,technical

# 自訂GPT-4.1整合風格
python main.py --input data.jsonl --output results.jsonl --gpt41-style warm,professional

# 使用自訂配置文件
python main.py --input data.jsonl --output results.jsonl --config custom_config.yaml

# 批次處理控制
python main.py --input data.jsonl --output results.jsonl --batch-size 10 --max-workers 6

# 啟用中間結果儲存
python main.py --input data.jsonl --output results.jsonl --save-intermediate

# 從中斷點恢復處理
python main.py --input data.jsonl --output results.jsonl --resume-from checkpoint_001.json
```

### 2. 參數調整方式

使用者可以透過函數參數直接調整處理行為：

**思考深度調整**：

- 使用 `--gemini-depth` 參數（light/moderate/deep）
- 使用 `--gpt4o-focus` 參數指定重點領域

**處理重點調整**：

- 透過 `--focus-areas` 參數指定分析角度
- 使用 `--integration-style` 參數調整整合風格

**輸出風格調整**：

- 使用 `--output-style` 參數調整專業度與溫暖度
- 透過 `--practical-ratio` 參數控制實用建議比重

### 3. 進階自訂功能

```python
# 自訂提示詞範本
from prompts.prompt_manager import PromptManager

pm = PromptManager()

# 為特定問題類型建立專屬範本
custom_prompt = pm.create_custom_template(
    model='gpt4o',
    question_type='career',
    focus_areas=['practical_guidance', 'timing_analysis'],
    style='encouraging'
)

# 動態調整處理參數
pm.set_processing_style('gpt41', {
    'practicality': 'very_high',
    'warmth': 'high'
})
```

## 實施階段規劃

### 第一階段：核心功能建立（MVP）

**目標**：建立基本的三階段處理流程

- [ ] 實作基礎資料處理模組
- [ ] 建立 Gemini 思考生成器
- [ ] 實作 GPT-4o 思考深化器
- [ ] 完成 GPT-4.1 最終整合器
- [ ] 建立基本提示詞管理系統
- [ ] 完成單一問題完整流程測試

**預期成果**：能夠處理單一問題的完整思考增強流程

### 第二階段：系統擴展與優化

**目標**：加入批量處理與品質控制

- [ ] 實作多線程批量處理
- [ ] 建立錯誤處理與重試機制
- [ ] 完善提示詞配置系統
- [ ] 加入中間結果儲存功能
- [ ] 建立處理進度追蹤
- [ ] 實作中斷恢復功能

**預期成果**：穩定的批量處理系統

### 第三階段：進階功能與個人化

**目標**：提供靈活的自訂選項

- [ ] 建立動態提示詞調整介面
- [ ] 實作問題類型自動識別優化
- [ ] 加入處理品質分析工具
- [ ] 建立 A/B 測試框架
- [ ] 完成使用者回饋整合機制
- [ ] 建立效能監控與優化工具

**預期成果**：高度可訂製的智能思考增強系統

## 技術要求與相依性

### 必要套件

```txt
# AI 模型 API
google-generativeai>=0.3.0
openai>=1.0.0

# 資料處理
pandas>=1.5.0
jsonlines>=3.0.0

# 語言處理
langdetect>=1.0.9

# 並行處理
concurrent.futures (內建)
threading (內建)

# 工具類
tqdm>=4.64.0
click>=8.0.0
loguru>=0.6.0
```

### 環境需求

- Python 3.8+
- 充足的 API 配額（Gemini、OpenAI）
- 建議 16GB+ RAM（用於大量資料處理）
- SSD 儲存空間（用於中間結果儲存）

## 品質保證機制

### 1. 階段性驗證

- 每個處理階段都有輸出格式驗證
- 思考內容完整性檢查
- 語言一致性維護

### 2. 錯誤處理策略

- 自動重試機制
- 優雅降級處理
- 詳細錯誤日誌記錄

### 3. 效能監控

- 處理時間追蹤
- API 呼叫統計
- 記憶體使用監控
- 成功率統計

### 4. 可測試性設計

- 單元測試覆蓋
- 整合測試框架
- 效能基準測試
- 回歸測試自動化

## 擴展性考慮

### 1. 模型擴展

系統設計支援輕鬆加入新的 AI 模型：

- 標準化的模型介面
- 可插拔的處理模組
- 統一的配置管理

### 2. 領域擴展

當前專注占星學，但架構支援擴展到其他領域：

- 可配置的專業知識模板
- 領域特定的提示詞系統
- 靈活的評估標準

### 3. 語言擴展

目前支援中英文，可擴展支援更多語言：

- 多語言提示詞模板
- 語言特定的處理邏輯
- 文化背景考量機制
